/**
 * @create 王家麒@2022.12.23
 */
const fs = require('fs');
const { time: { currentTimestamp } } = require('@baidu/bat-util');;

const worker = require('./worker');
const { IS_DEBUG } = require('../../../config');
const { logger } = require('./logger');

let automator;
if (IS_DEBUG) {
    automator = require('../../../../../ui-automator/src/index');
}
else {
    automator = require('@baidu/bat-automator');
}

const getLogTag = (method) => `[sub.perf.${method}]`;

const getExecution = async () => {
    const db = worker.getDb('assess');
    let exeList = await db.all(
        'SELECT te.id, t.id AS taskId ' +
        'FROM task_execution te ' +
        'JOIN task t ON t.id = te.task_id ' +
        'JOIN plan p ON p.id = t.plan_id ' +
        'WHERE te.status = 3 AND p.type = 2 AND t.is_valid = 0'
    );
    if (0 === exeList.length) {
        return;
    }
    let executeItem = exeList[0];
    executeItem.caseList = await db.query(
        'task_case',
        {
            id: 'taskCaseId',
            package_name: 'packageName',
            task_options: 'taskOptions',
            case_id: 'caseId',
        },
        {
            task_id: executeItem.taskId,
        }
    );
    for (let caseItem of executeItem.caseList) {
        caseItem.sceneList = await db.query(
            'task_scene_quota',
            {
                id: 'sceneId',
                record_start_strategy: 'recordStart',
                record_finish_strategy: 'recordFinish'
            },
            {
                task_case_id: caseItem.taskCaseId
            }
        );
    }
    return executeItem;
};

const handlePerfParse = async (executionId, caseId, packageName) => {
    const db = worker.getDb('assess');
    let [{ taskExecutionQuotaId, perfRecord, processRecord }] = await db.query(
        'task_execution_quota',
        {
            id: 'taskExecutionQuotaId',
            perf_record: 'perfRecord',
            process_record: 'processRecord'
        },
        {
            case_id: caseId,
            task_execution_id: executionId
        }
    );
    perfRecord = JSON.parse(perfRecord);
    processRecord = JSON.parse(processRecord);
    if (perfRecord.traceFile) {
        if (!fs.existsSync(perfRecord.traceFile)) {
            throw new Error('记录文件不存在');
        }
        perfRecord = await automator.prasePerfTrace(
            perfRecord.traceFile,
            perfRecord.processName,
            perfRecord.startTime,
            perfRecord.endTime
        );
        perfRecord = await handleInstrument2Tidevice(perfRecord);
    }
    if (perfRecord.perfetto && perfRecord.perfetto.isOk) {
        try {
            perfRecord.perfetto.praseRes =
                await automator.prasePerfettoFile(
                    perfRecord.perfetto.traceFile,
                    perfRecord.perfetto.startTime,
                    packageName
                );
        }
        catch (err) {
            logger.info(
                `${getLogTag('handlePerfParse')} execution ${executionId} case ${caseId}` +
                ` perfetto 处理失败 ${err.stack}`
            );
            delete perfRecord.perfetto;
        }
    }
    else {
        delete perfRecord.perfetto;
    }
    return { taskExecutionQuotaId, perfData: perfRecord, processRecord };
};

const handleInstrument2Tidevice = async perfRecord => {
    let tideviceRes = {
        fps: [],
        hitch: [],
        cpu: [],
        mem: [],
        gpu: [],
        network: [],
        tasks: []
    };
    // 处理 hitch
    tideviceRes.hitch = perfRecord.hitch;
    // 处理 cpu
    for (let { value, timestamp } of perfRecord.cpu) {
        tideviceRes.cpu.push({
            value: {
                total: value.toFixed(2),
                system: '0.00'
            },
            timestamp
        });
    }
    // 处理 mem
    for (let { value, timestamp } of perfRecord.mem) {
        tideviceRes.mem.push({
            value: {
                total: parseInt(value, 10)
            },
            timestamp
        });
    }
    // 处理 network
    for (let { upFlow, downFlow, total, timestamp } of perfRecord.network) {
        tideviceRes.network.push({
            value: {
                downFlow,
                upFlow,
                total
            },
            timestamp
        });
    }
    // 处理 tasks
    for (let { value, timestamp } of perfRecord.tasks) {
        tideviceRes.tasks.push({
            value: {
                total: value
            },
            timestamp
        });
    }
    return tideviceRes;
};

const strategy2Timestamp = (strategy, processRecord) => {
    for (let key of Object.keys(strategy)) {
        if (-1 === ['recordStart', 'recordFinish'].indexOf(key)) {
            continue;
        }
        let [tag, index, type] = strategy[key].split('_');
        index = parseInt(index, 10);
        if ('case' === tag) {
            if ('start' === type) {
                strategy[key] = processRecord[0][0].timestamp;
            } else if ('end' === type) {
                strategy[key] = processRecord[processRecord.length - 1][
                    processRecord[processRecord.length - 1].length - 1].timestamp;
            }
        }
        else if ('steps' === tag) {
            if (index < 0) {
                index = 0;
            }
            if (index >= processRecord.length) {
                index = processRecord.length - 1;
            }
            if ('start' === type) {
                strategy[key] = processRecord[index][0].timestamp;
            } else if ('end' === type) {
                strategy[key] = processRecord[index][processRecord[index].length - 1].timestamp;
            }
        }
    }
    return strategy;
};

const handleScene = async (perfRecord, processRecord, sceneList, taskExecutionQuotaId, taskOptions) => {
    const assessDb = worker.getDb('assess');
    for (let scene of sceneList) {
        let strategy = JSON.parse(JSON.stringify(scene));
        scene = strategy2Timestamp(strategy, processRecord);
        // 获取对应场景性能数据
        let perfData = [];
        for (let quota of Object.keys(perfRecord)) {
            if (-1 !== ['startTime', 'traceFile', 'perfetto'].indexOf(quota)) {
                continue;
            }
            let _perfData = [];
            for (let quotaItem of perfRecord[quota]) {
                if (scene.recordStart <= quotaItem.timestamp && scene.recordFinish > quotaItem.timestamp) {
                    _perfData.push(quotaItem);
                }
            }
            perfData.push({
                name: quota,
                data: _perfData
            });
        }
        if (perfRecord.perfetto) {
            let shutterRes = {
                smallShutterTimes: 0,
                shutterTimes: 0,
                bigShutterTimes: 0
            }
            for (let frameItem of perfRecord.perfetto.praseRes) {
                if (
                    frameItem.timestamp - frameItem.cost >= scene.recordStart &&
                    frameItem.timestamp <= scene.recordFinish
                ) {
                    if (frameItem.cost >= taskOptions.bigShutterThreshold) {
                        shutterRes.bigShutterTimes++;
                    }
                    else if (frameItem.cost >= taskOptions.shutterThreshold) {
                        shutterRes.shutterTimes++;
                    }
                    else if (frameItem.cost >= taskOptions.smallShutterTimes) {
                        shutterRes.smallShutterTimes++;
                    }
                }
            }
            perfData.push({
                name: 'perfetto',
                data: {
                    isOk: true,
                    gotFrame: 0 !== perfRecord.perfetto.praseRes.length,
                    startTime: perfRecord.perfetto.startTime,
                    endTime: perfRecord.perfetto.endTime,
                    traceFile: perfRecord.perfetto.traceFile,
                    shutterRes
                }
            });
        }
        // 记录每个场景性能数据
        await assessDb.insert('task_execution_quota_scenes', {
            perf_data: JSON.stringify(perfData),
            scene_id: scene.sceneId,
            task_execution_quota_id: taskExecutionQuotaId,
            start_time: scene.recordStart,
            end_time: scene.recordFinish
        });
    }
};

const afterParse = async (executionId, errMsg) => {
    const assessDb = worker.getDb('assess');
    await assessDb.update('task_execution', {
        status: '' === errMsg ? 1 : 2,
        exception_msg: errMsg
    }, { id: executionId });
};

const work = async () => {
    // 获取待处理分帧的执行记录
    let executeItem = await getExecution();
    if (undefined === executeItem) {
        return;
    }
    // 处理分帧
    logger.info(`${getLogTag('work')} 开始处理 execution ${executeItem.id}`);
    let errMsg = '';
    try {
        for (let { caseId, sceneList, packageName, taskOptions } of executeItem.caseList) {
            let logTitle = `${getLogTag('work')} 开始处理 execution ${executeItem.id} case ${caseId}`;
            logger.info(`${logTitle} 数据解析`);
            let startTime = currentTimestamp();
            // 完成数据解析
            let { taskExecutionQuotaId, perfData, processRecord }
                = await handlePerfParse(executeItem.id, caseId, packageName);
            // 完成多场景梳理
            await handleScene(perfData, processRecord, sceneList, taskExecutionQuotaId, JSON.parse(taskOptions));
            logger.info(`${logTitle} 性能数据解析处理完毕 cost: ${currentTimestamp() - startTime}ms`);
        }
    }
    catch (err) {
        logger.error(err, getLogTag('work'));
        errMsg = err.message;
    }
    // 处理结束动作, 如状态控制等
    await afterParse(executeItem.id, errMsg);
    logger.info(`${getLogTag('work')} execution ${executeItem.id} 处理完毕`);
};

const run = async (interval = 1000) => {
    try {
        await work();
    } catch (err) {
        logger.error(err, getLogTag('run'));
    } finally {
        setTimeout(() => run(interval), interval);
    }
};

run();

/**
 * @create 邱立楷@2020.08.08
 */
const fs = require('fs');
const path = require('path');
const {
    shell: { exeSync },
    error: { ConfigError }
} = require('@baidu/bat-util');;
const { logger } = require('./logger');
const { IS_DEBUG, APPLE } = require('../../../config');
const { getCurrentExecutionStep } = require('./util/case');
const stepRun = require('./run-step/index');
const BaseWorker = require('../../../lib/BaseWorker');
const Dom = require('./dom/index');

let automator;
if (IS_DEBUG) {
    automator = require('../../../../../ui-automator/src/index');
}
else {
    automator = require('@baidu/bat-automator');
}

const getLogTag = method => `[worker.${method}]`;

class Worker extends BaseWorker {
    constructor() {
        super('device', logger);
        this.deviceMap = { android: {}, iOS: {} };
        this.wdaProjectPath = '';
        this.wdaP8FilePath = '';
        this.gotCertificate = false;
    }

    async _updateFrontDeviceMap() {
        let android = [];
        for (let id in this.deviceMap.android) {
            let { brand, marketName, status, statusMsg, taskId } = this.deviceMap.android[id];
            android.push({ deviceId: id, brand, marketName, status, statusMsg, taskId });
        }
        let iOS = [];
        for (let id in this.deviceMap.iOS) {
            let { brand, marketName, status, statusMsg, taskId } = this.deviceMap.iOS[id];
            iOS.push({ deviceId: id, brand, marketName, status, statusMsg, taskId });
        }
        await this.callFront('device.update', { android, iOS });
        await this.sendToFront('device.maps', { android, iOS });
    }

    checkCertificate(resourcesPath) {
        try {
            // 判断是否有 xcodebuild, 如果没有就不用证书
            exeSync('which xcodebuild');
            // 判断是否已经有证书了
            try {
                let res =
                    exeSync(`security find-certificate -a -c "${APPLE.CER_NAME}" -Z | grep ^SHA-1`);
                if (res.includes('SHA-1')) {
                    this.gotCertificate = true;
                    return;
                }
            }
            catch { }
            // 获取证书文件路径
            let certRootPath = '';
            if (IS_DEBUG) {
                certRootPath = path.join(__dirname, '../../../../../bat-wda/certificate');
            }
            else {
                certRootPath = path.join(resourcesPath, 'public/bat-wda/certificate');
            }
            let p12FilePath = path.join(certRootPath, 'LazyPerf.p12');
            // 导入 p12 文件
            exeSync(
                `security import "${p12FilePath}" -k ~/Library/Keychains/login.keychain -P "${APPLE.P12_PASSWORD}" -A`
            );
            this.gotCertificate = true;
        }
        catch (err) {
            logger.warn(`${getLogTag('checkCertificate')} 证书导入失败: ${err.stack}`);
            this.gotCertificate = false;
        }
    }

    updateWdaProjectPath(resourcesPath) {
        if (IS_DEBUG) {
            this.wdaProjectPath = path.join(__dirname, '../../../../../bat-wda', 'WebDriverAgent.xcodeproj');
            this.wdaP8FilePath = path.join(__dirname, '../../../../../bat-wda/certificate', 'LazyPerf.p8');
        }
        else {
            this.wdaProjectPath = path.join(resourcesPath, 'public/bat-wda', 'WebDriverAgent.xcodeproj');
            this.wdaP8FilePath = path.join(resourcesPath, 'public/bat-wda/certificate', 'LazyPerf.p8');
        }
        if (!fs.existsSync(this.wdaProjectPath)) {
            this.wdaProjectPath = '';
        }
        if (!fs.existsSync(this.wdaP8FilePath)) {
            this.wdaP8FilePath = '';
        }
    }

    getDeviceType(type) {
        return automator.getDeviceType(type);
    }

    setDeviceMap(newDeviceMap) {
        this.deviceMap = newDeviceMap;
    }

    async getConnectDeviceMap(type) {
        return await automator.devices(type);
    }

    getDeviceMap(type) {
        type = this.getDeviceType(type);
        if ('Android' === type) {
            return this.deviceMap.android;
        }
        else if ('iOS' === type) {
            return this.deviceMap.iOS;
        }
        else {
            throw new Error(`不支持该类型设备: ${type}`);
        }
    }

    getDevice(type, id) {
        let deviceMap = this.getDeviceMap(type);
        if (deviceMap[id]) {
            return deviceMap[id];
        }
        else {
            throw new ConfigError('未连接设备', '请通过 USB 连接设备');
        }
    }

    isDeviceRegister(type, id) {
        let deviceMap = this.getDeviceMap(type);
        if (deviceMap[id]) {
            return true;
        }
        else {
            return false;
        }
    }

    async registerDevice(type, id, brand, marketName) {
        let deviceMap = this.getDeviceMap(type);
        deviceMap[id] = {
            brand,
            marketName,
            status: 0, // 0 未初始化；1 初始化中；2 空闲；3 繁忙；4 异常；5 离线；6 离线中
            statusMsg: '',
            taskId: 0, // 占据设备的任务 ID
            handler: null,
            dom: null
        };
        logger.info(getLogTag('registerDevice') +
            ` [type:${type}] [id:${id}] [brand:${brand}] [marketName:${marketName}]`);
        await this._updateFrontDeviceMap();
        return deviceMap[id];
    }

    async initDevice(type, id, options = {}, times = 1) {
        const logTag = getLogTag('initDevice');
        let device = null;
        let status = 0;
        try {
            device = this.getDevice(type, id);
            status = device.status;
            device.status = 1;
            await this._updateFrontDeviceMap();

            if (device.handler && device.handler.close) {
                logger.info(`${logTag} [type:${device.handler.type}] [id:${device.handler.id}] ${times}st close start`);
                await device.handler.close();
                logger.info(`${logTag} [type:${device.handler.type}] [id:${device.handler.id}] ${times}st close over`);
                device.handler = null;
            }

            if (device.handler && 'iOS' === device.handler.type) {
                options.screenSize = device.handler.handler.screenSize;
            }

            if ('iOS' === type) {
                options.appleOption = APPLE;
                if (this.gotCertificate) {
                    options.appleOption.wdaProjectPath = this.wdaProjectPath;
                    options.appleOption.wdaP8FilePath = this.wdaP8FilePath;
                }
                else {
                    options.appleOption.wdaProjectPath = '';
                    options.appleOption.wdaP8FilePath = '';
                }
            }

            logger.info(`${logTag} [type:${type}] [id:${id}] ${times}st launch start`);
            device.handler = await automator.launch(id, type, options);
            logger.info(`${logTag} [type:${type}] [id:${id}] ${times}st launch over`);

            device.dom = new Dom(device.handler,
                (...params) => this.sendToPyServer(...params),
                (event, data) => {
                    this.sendToFront(event, data)
                }
            );

            if (0 === status || 1 === status || 4 === status || 5 === status || 6 === status) {
                device.status = 2;
            }
            else {
                device.status = status;
            }
            await this._updateFrontDeviceMap();
            return device;
        }
        catch (err) {
            if ('ConfigError' === err.type) {
                device.status = 4;
                device.statusMsg = err.message;
                await this._updateFrontDeviceMap();
                await this.sendToFront('notification.warn', {
                    title: '配置异常',
                    desc: err.message,
                    detail: err.detail
                });
                throw err;
            }
            else {
                logger.warn(`${logTag} [type:${type}] [id:${id}] ${times}st ${err.message}`);
                if ('iOS' === type) {
                    if (times >= 3) {
                        if (3 === times) {
                            device.status = status;
                            logger.info(`${logTag} [type:${type}] [id:${id}] ${times}st reboot`);
                            await automator.reboot(type, id);
                            return await this.initDevice(type, id, options, times + 1);
                        }
                        else {
                            device.status = 4;
                            device.statusMsg = err.message;
                            await this._updateFrontDeviceMap();
                            throw err;
                        }
                    }
                    else {
                        device.status = status;
                        return await this.initDevice(type, id, options, times + 1);
                    }
                }
                else {
                    if (times > 3) {
                        device.status = 4;
                        device.statusMsg = `重试 3 次初始化失败: ${err.message}`;
                        await this._updateFrontDeviceMap();
                        await this.sendToFront('notification.error', {
                            title: '设备异常',
                            desc: '设备初始化失败',
                            detail: '请联系 LazyPerf 开发者'
                        });
                        throw err;
                    }
                    else {
                        device.status = status;
                        return await this.initDevice(type, id, options, times + 1);
                    }
                }
            }
        }
    }

    async offlineDevice(type, id) {
        const logTag = getLogTag('offlineDevice');
        let device = this.getDevice(type, id);
        if (1 === device.status || 6 === device.status) {
            logger.warn(`${logTag} 设备 ${type}-${id} 初始化/离线中，无法离线`);
            return false;
        }
        else if (5 === device.status) {
            return false;
        }
        else {
            device.status = 6; // 离线中
            if (device.handler && device.handler.close) {
                await device.handler.close();
            }
            device.status = 5; // 离线
            logger.info(`${logTag} ${type}-${id}`);
            await this._updateFrontDeviceMap();
            return true;
        }
    }

    async occupyDevice(type, id, taskId) {
        const logTag = getLogTag('occupyDevice');
        try {
            let device = this.getDevice(type, id);
            if (2 === device.status) { // 空闲
                device.status = 3; // 繁忙
                device.taskId = taskId;
                await this._updateFrontDeviceMap();
                logger.info(`${logTag} [type:${type}] [id:${id}] [task:${taskId}]`);
                return true;
            }
            return false;
        }
        catch (err) {
            return false;
        }
    }

    async freeDevice(type, id, taskId = 0) {
        const logTag = getLogTag('freeDevice');
        logger.info(`${logTag} 释放设备开始: task-${taskId} ${type}-${id}`);
        try {
            let device = this.getDevice(type, id);
            if (0 === taskId || device.taskId === taskId) {
                // 0 未初始化；1 初始化中；2 空闲；3 繁忙；4 异常；5 离线；6 离线中
                if (3 === device.status) {
                    device.status = 2; // 空闲
                }
                device.taskId = 0;
                await this._updateFrontDeviceMap();
                logger.info(`${logTag} 释放设备完成: task-${taskId} ${type}-${id}`);
                return true;
            }
            return false;
        }
        catch (err) {
            return false;
        }
    }

    async domCall(action, { type, id, needDevice = true }, ...params) {
        if (needDevice) {
            let { status, dom } = this.getDevice(type, id);
            if (2 !== status) {
                throw new Error('设备不处于空闲状态');
            }
            return await dom[action](...params);
        }
        // 兼容有些 dom 处理不需要设备的情况
        else {
            let dom = new Dom(
                {},
                (...params) => this.sendToPyServer(...params),
                (event, data) => { this.sendToFront(event, data); }
            );
            return await dom[action](...params);
        }
    }

    async deviceCall(action, { type, id }, ...params) {
        let { status, handler } = this.getDevice(type, id);
        if (2 !== status) {
            throw new Error('设备不处于空闲状态');
        }
        return await handler[action](...params);
    }

    async runSingleStep(deviceType, deviceId, step, params) {
        let device = await this.getDevice(deviceType, deviceId);
        if (2 !== device.status) {
            throw new Error('设备不处于空闲状态');
        }
        step = await getCurrentExecutionStep(step, params, 0);
        return await stepRun(device.handler, device.dom, step, 0, 0);
    }
}

let worker = new Worker();
worker.ready();

module.exports = worker;
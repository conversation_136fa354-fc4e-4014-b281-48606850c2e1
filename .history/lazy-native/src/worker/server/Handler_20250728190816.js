/**
 * @create 邱立楷@2022.08.08
 */
const path = require('path');
const axios = require('axios');
const getPort = require('get-port');
const childProcess = require('child_process');
const {
    time: { currentTimestamp, delay },
    error: { JsonrpcError }
} = require('@baidu/bat-util');;

const { logger } = require('../../lib/logger');

const { IS_DEBUG } = require('../../config');
const WORKER_NAME = 'localServer';
const getLogTag = method => `[${WORKER_NAME}.${method}]`;

module.exports = class LocalServerHandler {
    constructor() {
        this.name = WORKER_NAME;
        if (IS_DEBUG) {
            this.python = path.join(__dirname, '../../../../lazy-strategy/venv/bin', 'python3.9');
            this.script = path.join(__dirname, '../../../../lazy-strategy', 'test_server.py');
        }
        else {
            this.script = path.join(process.resourcesPath, 'public/py/main', 'main');
        }
        this.id = 0;
        this.port = 9898;
        this.process = null;
        process.on('exit', code => this.close(code));
    }

    close(code) {
        let signal = 'SIGHUP';
        if (0 !== code) {
            signal = 'SIGINT';
        }
        if (this.process && this.process.kill) {
            this.process.kill(signal);
        }
    }

    async init(timeout = 60000) {
        if (null === this.process) {
            return new Promise(async (resolve, reject) => {
                // 启动子进程
                if (IS_DEBUG) {
                    this.process = childProcess.spawn(this.python, [this.script, this.port, 'lazy-native']);
                }
                else {
                    this.port = await getPort();
                    this.process = childProcess.execFile(this.script, [this.port, 'lazy-native']);
                }

                // 监听启动失败
                this.process.on('close', (code, signal) => {
                    this.process = null;
                    let tag = getLogTag('close');
                    logger.info(`${tag} ${code}, ${signal}`);
                    reject(new Error(`${WORKER_NAME} 初始化失败, code: ${code}`));
                });

                this.process.stdout.on('data', res => { });
                this.process.stderr.on('data', res => { });

                // 监听启动成功
                let tag = getLogTag('init');
                let expiredTime = currentTimestamp() + timeout;
                while (currentTimestamp() < expiredTime) {
                    await delay(500);
                    let isStart = await this.send('status').catch(() => false);
                    if (isStart) {
                        resolve(true);
                        logger.info(`${tag} 启动成功，端口: ${this.port}; 调试模式: ${IS_DEBUG}`);
                        return;
                    }
                }
                reject(new Error(`${WORKER_NAME} 初始化超时`));
            });
        }
    }

    async send(method, params = [], timeout = 120000) {
        try {
            let { result, error } = await axios.post(
                `http://127.0.0.1:${this.port}`,
                {
                    jsonrpc: '2',
                    id: this.id++,
                    method,
                    params
                },
                { timeout }
            ).then(res => res.data);

            logger.debug(`${getLogTag('send')} 请求方法: ${method} 请求参数: ${JSON.stringify(params)} 请求结果: ${JSON.stringify(result)}`);

            if (error) {
                throw new JsonrpcError(error);
            }
            else {
                return result;
            }
        }
        catch (err) {
            if ('status' === method) {
                throw err;
            }

            logger.error(getLogTag('send')
                + `[method:${method}] 请求失败 ${err.message} ${err.stack}`);

            if ('JsonrpcError' === err.type) {
                throw err;
            }
            else {
                throw new JsonrpcError({
                    code: -32300,
                    message: 'localServer 请求失败',
                    data: {
                        method,
                        message: err.message,
                        stack: err.stack
                    }
                });
            }
        }
    }


};
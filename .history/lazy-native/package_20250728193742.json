{"private": true, "version": "3.11.0", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "lazyPerf2.0", "main": "./src/index.js", "scripts": {"start": "cross-env NODE_ENV=debug electron .", "dev": "cross-env NODE_ENV=dev PATH=\"/Users/<USER>/Library/Python/3.9/bin:$PATH\" electron .", "tti": "node test/test-tti/index.js && cross-env NODE_ENV=dev electron .", "launch": "node test/test-launch/index.js && cross-env NODE_ENV=dev electron .", "build-mac": "node scripts/before-upload.js && ./node_modules/.bin/electron-builder --mac", "build-win": "node scripts/before-upload.js && ./node_modules/.bin/electron-builder --win --x64", "upload-pack": "node scripts/upload.js"}, "dependencies": {"@baiducloud/sdk": "^1.0.0-rc.36", "@baidu/bat-automator": "2.1.193", "@baidu/bat-util": "^8.0.68", "axios": "1.1.3", "get-port": "^5.1.1", "cacheable-lookup": "^6.0.4", "sharp": "0.30.6", "sqlite3": "5.1.6", "xmldom": "^0.6.0"}, "devDependencies": {"cross-env": "^7.0.3", "electron": "20.3.8", "electron-builder": "23.6.0", "electron-packager": "15.5.2", "electron-rebuild": "3.2.9"}, "build": {"forceCodeSigning": false, "mac": {"identity": "undefined", "hardenedRuntime": false, "entitlements": "build/entitlements.mac.plist", "extendInfo": {"NSCameraUsageDescription": "iOS 录屏需要使用摄像头权限", "com.apple.security.device.camera": true}}, "electronDownload": {"mirror": "https://registry.npmmirror.com/-/binary/electron/"}, "extraResources": ["./public/py/**", "./public/bat-wda/**"]}, "author": "longbatian"}
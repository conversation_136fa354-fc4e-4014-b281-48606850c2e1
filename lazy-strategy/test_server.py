#!/usr/bin/env python3
# -*- coding: UTF-8 -*-

"""
简化的测试服务器，用于测试Electron应用启动
"""

import sys
import json
from flask import Flask, request

app = Flask(__name__)

@app.route("/", methods=['POST'])
def index():
    """ 统一接口 """
    try:
        jsonrpc = request.json["jsonrpc"]
        id = request.json["id"]
        method = request.json["method"]
        params = request.json["params"]

        print(f'method: {method}')
        print(f'params: {params}')

        # 简单的状态检查响应
        if method == "status":
            return {
                "jsonrpc": jsonrpc,
                "id": id,
                "result": True
            }
        
        # 其他方法返回空结果
        return {
            "jsonrpc": jsonrpc,
            "id": id,
            "result": {}
        }
        
    except Exception as e:
        print(f'Error: {e}')
        return {
            "jsonrpc": "2.0",
            "id": request.json.get("id", 0),
            "error": {
                "code": -32603,
                "message": str(e)
            }
        }

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python test_server.py <port> [name]")
        sys.exit(1)
    
    port = int(sys.argv[1])
    name = sys.argv[2] if len(sys.argv) > 2 else "test-server"
    
    print(f"Starting test server on port {port} for {name}")
    app.run(host='127.0.0.1', port=port, debug=False)

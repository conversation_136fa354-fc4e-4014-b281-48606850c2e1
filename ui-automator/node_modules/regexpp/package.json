{"name": "regexpp", "version": "2.0.1", "description": "Regular expression parser for ECMAScript 2018.", "engines": {"node": ">=6.5.0"}, "main": "index", "files": ["index.*"], "dependencies": {}, "devDependencies": {"@types/eslint": "^4.16.2", "@types/mocha": "^5.2.2", "@types/node": "^10.3.3", "coveralls": "^3.0.1", "dts-bundle": "^0.7.3", "eslint": "^4.19.1", "eslint-plugin-mysticatea": "^5.0.0-beta.15", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^12.0.2", "rimraf": "^2.6.2", "rollup": "^0.60.7", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-watch": "^4.3.1", "ts-node": "^6.1.1", "typescript": "^2.9.2", "typescript-eslint-parser": "^16.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "run-s build:*", "build:tsc": "tsc --module es2015", "build:rollup": "rollup -c", "build:dts": "dts-bundle --name regexpp --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .temp index.*", "coveralls": "nyc report --reporter=text-lcov | coveralls", "lint": "eslint scripts src test --ext .ts", "pretest": "run-s build lint", "test": "nyc _mocha \"test/*.ts\" --reporter dot --timeout 10000", "update:test": "ts-node scripts/update-fixtures.ts", "update:ids": "ts-node scripts/update-unicode-ids.ts", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "_mocha \"test/*.ts\" --require ts-node/register --reporter dot --timeout 10000 --watch-extensions .ts --watch --growl"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/regexpp.git"}, "keywords": ["regexp", "regular", "expression", "parser", "validator", "ast", "abstract", "syntax", "tree", "ecmascript", "es2015", "es2016", "es2017", "es2018", "annexB"], "author": "<PERSON><PERSON> (https://github.com/mysticatea)", "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/regexpp/issues"}, "homepage": "https://github.com/mysticatea/regexpp#readme"}
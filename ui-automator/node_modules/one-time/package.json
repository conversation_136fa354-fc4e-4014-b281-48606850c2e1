{"name": "one-time", "version": "1.0.0", "description": "Run the supplied function exactly one time (once)", "main": "index.js", "scripts": {"test:runner": "mocha test.js", "test:watch": "npm run test:runner -- --watch", "test": "nyc --reporter=text --reporter=json-summary npm run test:runner"}, "repository": {"type": "git", "url": "https://github.com/3rd-Eden/one-time.git"}, "keywords": ["once", "function", "single", "one", "one-time", "execution", "nope"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"assume": "^2.2.0", "mocha": "^6.1.4", "nyc": "^14.1.0"}, "dependencies": {"fn.name": "1.x.x"}}
{"name": "bat-adbkit", "version": "2.11.12", "description": "A pure Node.js client for the Android Debug Bridge.", "keywords": ["adb", "adbkit", "android", "logcat", "monkey"], "bin": {"adbkit": "./bin/adbkit"}, "bugs": {"url": "https://github.com/openstf/adbkit/issues"}, "license": "Apache-2.0", "author": {"name": "The OpenSTF Project", "email": "<EMAIL>", "url": "https://openstf.io"}, "main": "./index", "repository": {"type": "git", "url": "https://github.com/sharenaive/adbkit"}, "scripts": {"postpublish": "grunt clean", "prepublish": "grunt test coffee", "test": "grunt test"}, "dependencies": {"adbkit-logcat": "^1.1.0", "adbkit-monkey": "~1.0.1", "bluebird": "~2.9.24", "commander": "^2.3.0", "debug": "~2.6.3", "node-forge": "^0.7.1", "split": "~0.3.3"}, "devDependencies": {"bench": "~0.3.5", "chai": "~2.2.0", "coffee-script": "~1.9.1", "coffeelint": "~1.9.3", "grunt": "~0.4.5", "grunt-cli": "~0.1.13", "grunt-coffeelint": "0.0.13", "grunt-contrib-clean": "~0.6.0", "grunt-contrib-coffee": "~0.13.0", "grunt-contrib-watch": "~0.6.1", "grunt-exec": "~0.4.3", "grunt-jsonlint": "~1.0.4", "grunt-notify": "~0.4.1", "mocha": "~2.2.1", "sinon": "~1.14.1", "sinon-chai": "~2.7.0"}, "engines": {"node": ">= 0.10.4"}}
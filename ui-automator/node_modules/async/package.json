{"name": "async", "description": "Higher-order functions and common patterns for asynchronous code", "main": "./lib/async", "author": "<PERSON><PERSON>", "version": "0.2.10", "repository": {"type": "git", "url": "https://github.com/caolan/async.git"}, "bugs": {"url": "https://github.com/caolan/async/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/caolan/async/raw/master/LICENSE"}], "devDependencies": {"nodeunit": ">0.0.0", "uglify-js": "1.2.x", "nodelint": ">0.0.0"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "scripts": {"test": "nodeunit test/test-async.js"}}
{"name": "astral-regex", "version": "1.0.0", "description": "Regular expression for matching astral symbols", "license": "MIT", "repository": "kevva/astral-regex", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["astral", "emoji", "regex", "surrogate"], "dependencies": {}, "devDependencies": {"ava": "*", "xo": "*"}}
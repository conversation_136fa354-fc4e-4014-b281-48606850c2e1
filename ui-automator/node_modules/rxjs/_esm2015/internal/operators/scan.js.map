{"version": 3, "file": "scan.js", "sources": ["../../../src/internal/operators/scan.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAoD3C,MAAM,UAAU,IAAI,CAAO,WAAmD,EAAE,IAAY;IAC1F,IAAI,OAAO,GAAG,KAAK,CAAC;IAMpB,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE;QACzB,OAAO,GAAG,IAAI,CAAC;KAChB;IAED,OAAO,SAAS,oBAAoB,CAAC,MAAqB;QACxD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;IACnE,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,YAAY;IAChB,YAAoB,WAAmD,EAAU,IAAY,EAAU,UAAmB,KAAK;QAA3G,gBAAW,GAAX,WAAW,CAAwC;QAAU,SAAI,GAAJ,IAAI,CAAQ;QAAU,YAAO,GAAP,OAAO,CAAiB;IAAG,CAAC;IAEnI,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACrG,CAAC;CACF;AAOD,MAAM,cAAqB,SAAQ,UAAa;IAY9C,YAAY,WAA0B,EAAU,WAAmD,EAAU,KAAY,EACrG,OAAgB;QAClC,KAAK,CAAC,WAAW,CAAC,CAAC;QAF2B,gBAAW,GAAX,WAAW,CAAwC;QAAU,UAAK,GAAL,KAAK,CAAO;QACrG,YAAO,GAAP,OAAO,CAAS;QAZ5B,UAAK,GAAW,CAAC,CAAC;IAc1B,CAAC;IAZD,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,IAAI,IAAI,CAAC,KAAY;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAOS,KAAK,CAAC,KAAQ;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;YAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC9B;aAAM;YACL,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SAC7B;IACH,CAAC;IAEO,QAAQ,CAAC,KAAQ;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,MAAW,CAAC;QAChB,IAAI;YACF,MAAM,GAAG,IAAI,CAAC,WAAW,CAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SACvD;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SAC7B;QACD,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;QACnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;CACF"}
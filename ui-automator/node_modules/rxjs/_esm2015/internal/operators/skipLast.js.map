{"version": 3, "file": "skipLast.js", "sources": ["../../../src/internal/operators/skipLast.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,uBAAuB,EAAE,MAAM,iCAAiC,CAAC;AA0C1E,MAAM,UAAU,QAAQ,CAAI,KAAa;IACvC,OAAO,CAAC,MAAqB,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7E,CAAC;AAED,MAAM,gBAAgB;IACpB,YAAoB,UAAkB;QAAlB,eAAU,GAAV,UAAU,CAAQ;QACpC,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,uBAAuB,CAAC;SACnC;IACH,CAAC;IAED,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;YAGzB,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;SACrD;aAAM;YACL,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;SAC9E;IACH,CAAC;CACF;AAOD,MAAM,kBAAsB,SAAQ,UAAa;IAI/C,YAAY,WAA0B,EAAU,UAAkB;QAChE,KAAK,CAAC,WAAW,CAAC,CAAC;QAD2B,eAAU,GAAV,UAAU,CAAQ;QAF1D,WAAM,GAAW,CAAC,CAAC;QAIzB,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAI,UAAU,CAAC,CAAC;IACxC,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAE5B,IAAI,KAAK,GAAG,SAAS,EAAE;YACrB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;SAC3B;aAAM;YACL,MAAM,YAAY,GAAG,KAAK,GAAG,SAAS,CAAC;YACvC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;YACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;YAEpC,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjC;IACH,CAAC;CACF"}
{"version": 3, "file": "take.js", "sources": ["../../../src/internal/operators/take.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,uBAAuB,EAAE,MAAM,iCAAiC,CAAC;AAC1E,OAAO,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAkD5C,MAAM,UAAU,IAAI,CAAI,KAAa;IACnC,OAAO,CAAC,MAAqB,EAAE,EAAE;QAC/B,IAAI,KAAK,KAAK,CAAC,EAAE;YACf,OAAO,KAAK,EAAE,CAAC;SAChB;aAAM;YACL,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;SAC7C;IACH,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,YAAY;IAChB,YAAoB,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;QAC/B,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;YAClB,MAAM,IAAI,uBAAuB,CAAC;SACnC;IACH,CAAC;IAED,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACtE,CAAC;CACF;AAOD,MAAM,cAAkB,SAAQ,UAAa;IAG3C,YAAY,WAA0B,EAAU,KAAa;QAC3D,KAAK,CAAC,WAAW,CAAC,CAAC;QAD2B,UAAK,GAAL,KAAK,CAAQ;QAFrD,UAAK,GAAW,CAAC,CAAC;IAI1B,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,KAAK,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAC3B,IAAI,KAAK,IAAI,KAAK,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7B,IAAI,KAAK,KAAK,KAAK,EAAE;gBACnB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;aACpB;SACF;IACH,CAAC;CACF"}
{"version": 3, "file": "timeoutWith.js", "sources": ["../../../src/internal/operators/timeoutWith.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAE3C,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,qBAAqB,EAAE,cAAc,EAAE,qBAAqB,EAAE,MAAM,mBAAmB,CAAC;AA2DjG,MAAM,UAAU,WAAW,CAAO,GAAkB,EAClB,cAAkC,EAClC,YAA2B,KAAK;IAChE,OAAO,CAAC,MAAqB,EAAE,EAAE;QAC/B,IAAI,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,OAAO,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAS,GAAG,CAAC,CAAC;QACjF,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAC,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC;IACnG,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,mBAAmB;IACvB,YAAoB,OAAe,EACf,eAAwB,EACxB,cAAoC,EACpC,SAAwB;QAHxB,YAAO,GAAP,OAAO,CAAQ;QACf,oBAAe,GAAf,eAAe,CAAS;QACxB,mBAAc,GAAd,cAAc,CAAsB;QACpC,cAAS,GAAT,SAAS,CAAe;IAC5C,CAAC;IAED,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,qBAAqB,CAC/C,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CACpF,CAAC,CAAC;IACL,CAAC;CACF;AAOD,MAAM,qBAA4B,SAAQ,qBAA2B;IAInE,YAAY,WAA0B,EAClB,eAAwB,EACxB,OAAe,EACf,cAAoC,EACpC,SAAwB;QAC1C,KAAK,CAAC,WAAW,CAAC,CAAC;QAJD,oBAAe,GAAf,eAAe,CAAS;QACxB,YAAO,GAAP,OAAO,CAAQ;QACf,mBAAc,GAAd,cAAc,CAAsB;QACpC,cAAS,GAAT,SAAS,CAAe;QAE1C,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEO,MAAM,CAAC,eAAe,CAAO,UAAuC;QAC1E,MAAM,EAAE,cAAc,EAAE,GAAG,UAAU,CAAC;QACtC,UAAU,CAAC,sBAAsB,EAAE,CAAC;QACpC,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,cAAc,EAAE,IAAI,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC;IAEO,eAAe;QACrB,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QACxB,IAAI,MAAM,EAAE;YAMV,IAAI,CAAC,MAAM,GAAmD,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAE,CAAC;SACpG;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAmD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAC5F,qBAAqB,CAAC,eAAsB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAChE,CAAC,CAAC;SACL;IACH,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,EAAE,CAAC;SACxB;QACD,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAGD,YAAY;QACV,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,IAAK,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG,IAAK,CAAC;IAC9B,CAAC;CACF"}
{"version": 3, "file": "windowTime.js", "sources": ["../../../src/internal/operators/windowTime.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAErC,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AA+FlD,MAAM,UAAU,UAAU,CAAI,cAAsB;IAClD,IAAI,SAAS,GAAkB,KAAK,CAAC;IACrC,IAAI,sBAAsB,GAAW,IAAI,CAAC;IAC1C,IAAI,aAAa,GAAW,MAAM,CAAC,iBAAiB,CAAC;IAErD,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KAC1B;IAED,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KAC1B;SAAM,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAClC,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;KACtC;IAED,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KAC1B;SAAM,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAClC,sBAAsB,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/C;IAED,OAAO,SAAS,0BAA0B,CAAC,MAAqB;QAC9D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAI,cAAc,EAAE,sBAAsB,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;IAClH,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,kBAAkB;IAEtB,YAAoB,cAAsB,EACtB,sBAAqC,EACrC,aAAqB,EACrB,SAAwB;QAHxB,mBAAc,GAAd,cAAc,CAAQ;QACtB,2BAAsB,GAAtB,sBAAsB,CAAe;QACrC,kBAAa,GAAb,aAAa,CAAQ;QACrB,cAAS,GAAT,SAAS,CAAe;IAC5C,CAAC;IAED,IAAI,CAAC,UAAqC,EAAE,MAAW;QACrD,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,oBAAoB,CAC9C,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CACjG,CAAC,CAAC;IACL,CAAC;CACF;AA0BD,MAAM,cAAkB,SAAQ,OAAU;IAA1C;;QACU,0BAAqB,GAAW,CAAC,CAAC;IAU5C,CAAC;IARC,IAAI,CAAC,KAAS;QACZ,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpB,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC,CAAC;CACF;AAOD,MAAM,oBAAwB,SAAQ,UAAa;IAGjD,YAAsB,WAAsC,EACxC,cAAsB,EACtB,sBAAqC,EACrC,aAAqB,EACrB,SAAwB;QAC1C,KAAK,CAAC,WAAW,CAAC,CAAC;QALC,gBAAW,GAAX,WAAW,CAA2B;QACxC,mBAAc,GAAd,cAAc,CAAQ;QACtB,2BAAsB,GAAtB,sBAAsB,CAAe;QACrC,kBAAa,GAAb,aAAa,CAAQ;QACrB,cAAS,GAAT,SAAS,CAAe;QANpC,YAAO,GAAwB,EAAE,CAAC;QASxC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,sBAAsB,KAAK,IAAI,IAAI,sBAAsB,IAAI,CAAC,EAAE;YAClE,MAAM,UAAU,GAAkB,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAO,IAAI,EAAE,CAAC;YACnF,MAAM,aAAa,GAAqB,EAAE,cAAc,EAAE,sBAAsB,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;YAChH,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAgB,mBAAmB,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC;YAC7F,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAmB,sBAAsB,EAAE,sBAAsB,EAAE,aAAa,CAAC,CAAC,CAAC;SAC/G;aAAM;YACL,MAAM,iBAAiB,GAAyB,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC;YAC7F,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAuB,0BAA0B,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC;SACnH;IACH,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAClB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,IAAI,MAAM,CAAC,oBAAoB,IAAI,IAAI,CAAC,aAAa,EAAE;oBACrD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;iBAC1B;aACF;SACF;IACH,CAAC;IAES,MAAM,CAAC,GAAQ;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SAC5B;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAES,SAAS;QACjB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAClB,MAAM,CAAC,QAAQ,EAAE,CAAC;aACnB;SACF;QACD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAEM,UAAU;QACf,MAAM,MAAM,GAAG,IAAI,cAAc,EAAK,CAAC;QACvC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzB,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,WAAW,CAAC,MAAyB;QAC1C,MAAM,CAAC,QAAQ,EAAE,CAAC;QAClB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7C,CAAC;CACF;AAED,SAAS,0BAA0B,CAAiD,KAA2B;IAC7G,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;IACrD,IAAI,MAAM,EAAE;QACV,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;KAChC;IACD,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;IACvC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,sBAAsB,CAA6C,KAAuB;IACjG,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,sBAAsB,EAAE,GAAG,KAAK,CAAC;IAChF,MAAM,MAAM,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;IACvC,MAAM,MAAM,GAAG,IAAI,CAAC;IACpB,IAAI,OAAO,GAA0B,EAAE,MAAM,EAAE,YAAY,EAAO,IAAI,EAAE,CAAC;IACzE,MAAM,aAAa,GAAkB,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;IACrE,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAgB,mBAAmB,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;IAC7G,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IACjC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,sBAAsB,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,mBAAmB,CAAI,KAAoB;IAClD,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;IAC9C,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,YAAY,EAAE;QACrD,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;KAC7C;IACD,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACjC,CAAC"}
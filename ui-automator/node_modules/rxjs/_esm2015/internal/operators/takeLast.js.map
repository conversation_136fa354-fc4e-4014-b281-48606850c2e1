{"version": 3, "file": "takeLast.js", "sources": ["../../../src/internal/operators/takeLast.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,uBAAuB,EAAE,MAAM,iCAAiC,CAAC;AAC1E,OAAO,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AA8C5C,MAAM,UAAU,QAAQ,CAAI,KAAa;IACvC,OAAO,SAAS,wBAAwB,CAAC,MAAqB;QAC5D,IAAI,KAAK,KAAK,CAAC,EAAE;YACf,OAAO,KAAK,EAAE,CAAC;SAChB;aAAM;YACL,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;SACjD;IACH,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,gBAAgB;IACpB,YAAoB,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;QAC/B,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;YAClB,MAAM,IAAI,uBAAuB,CAAC;SACnC;IACH,CAAC;IAED,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1E,CAAC;CACF;AAOD,MAAM,kBAAsB,SAAQ,UAAa;IAI/C,YAAY,WAA0B,EAAU,KAAa;QAC3D,KAAK,CAAC,WAAW,CAAC,CAAC;QAD2B,UAAK,GAAL,KAAK,CAAQ;QAHrD,SAAI,GAAa,IAAI,KAAK,EAAE,CAAC;QAC7B,UAAK,GAAW,CAAC,CAAC;IAI1B,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAE3B,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAClB;aAAM;YACL,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;SACrB;IACH,CAAC;IAES,SAAS;QACjB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAEvB,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACjE,MAAM,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC;YAExB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;gBAC9B,MAAM,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;gBAC9B,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;aAC7B;SACF;QAED,WAAW,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;CACF"}
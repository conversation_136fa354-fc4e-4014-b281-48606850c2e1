{"version": 3, "file": "multicast.js", "sources": ["../../../src/internal/operators/multicast.ts"], "names": [], "mappings": "AAIA,OAAO,EAAyB,+BAA+B,EAAE,MAAM,qCAAqC,CAAC;AA6B7G,MAAM,UAAU,SAAS,CAAO,uBAAwD,EACxD,QAAmD;IACjF,OAAO,SAAS,yBAAyB,CAAC,MAAqB;QAC7D,IAAI,cAAgC,CAAC;QACrC,IAAI,OAAO,uBAAuB,KAAK,UAAU,EAAE;YACjD,cAAc,GAAqB,uBAAuB,CAAC;SAC5D;aAAM;YACL,cAAc,GAAG,SAAS,cAAc;gBACtC,OAAmB,uBAAuB,CAAC;YAC7C,CAAC,CAAC;SACH;QAED,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YAClC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,CAAC;SACrE;QAED,MAAM,WAAW,GAAQ,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,+BAA+B,CAAC,CAAC;QAChF,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;QAC5B,WAAW,CAAC,cAAc,GAAG,cAAc,CAAC;QAE5C,OAAkC,WAAW,CAAC;IAChD,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,iBAAiB;IAC5B,YAAoB,cAAgC,EAChC,QAAkD;QADlD,mBAAc,GAAd,cAAc,CAAkB;QAChC,aAAQ,GAAR,QAAQ,CAA0C;IACtE,CAAC;IACD,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACtC,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC7D,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5C,OAAO,YAAY,CAAC;IACtB,CAAC;CACF"}
{"version": 3, "file": "windowToggle.js", "sources": ["../../../src/internal/operators/windowToggle.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAmD9D,MAAM,UAAU,YAAY,CAAO,QAAuB,EACvB,eAAkD;IACnF,OAAO,CAAC,MAAqB,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,oBAAoB,CAAO,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;AAC3G,CAAC;AAED,MAAM,oBAAoB;IAExB,YAAoB,QAAuB,EACvB,eAAkD;QADlD,aAAQ,GAAR,QAAQ,CAAe;QACvB,oBAAe,GAAf,eAAe,CAAmC;IACtE,CAAC;IAED,IAAI,CAAC,UAAqC,EAAE,MAAW;QACrD,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,sBAAsB,CAChD,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAChD,CAAC,CAAC;IACL,CAAC;CACF;AAYD,MAAM,sBAA6B,SAAQ,eAAuB;IAIhE,YAAY,WAAsC,EAC9B,QAAuB,EACvB,eAAkD;QACpE,KAAK,CAAC,WAAW,CAAC,CAAC;QAFD,aAAQ,GAAR,QAAQ,CAAe;QACvB,oBAAe,GAAf,eAAe,CAAmC;QAL9D,aAAQ,GAAuB,EAAE,CAAC;QAOxC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAe,CAAC,CAAC,CAAC;IACvF,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAC1B,IAAI,QAAQ,EAAE;YACZ,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBAC5B,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAChC;SACF;IACH,CAAC;IAES,MAAM,CAAC,GAAQ;QAEvB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,QAAQ,EAAE;YACZ,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC5B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;YAEf,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE;gBACpB,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAChC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC1B,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;aACpC;SACF;QAED,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAES,SAAS;QACjB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,QAAQ,EAAE;YACZ,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC5B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;YACf,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE;gBACpB,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAChC,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC1B,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;aACpC;SACF;QACD,KAAK,CAAC,SAAS,EAAE,CAAC;IACpB,CAAC;IAGD,YAAY;QACV,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,QAAQ,EAAE;YACZ,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC5B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;YACf,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE;gBACpB,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAChC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC7B,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;aACpC;SACF;IACH,CAAC;IAED,UAAU,CAAC,UAAe,EAAE,UAAe,EAChC,UAAkB,EAAE,UAAkB,EACtC,QAAiC;QAE1C,IAAI,UAAU,KAAK,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,eAAe,CAAC;YACpB,IAAI;gBACF,MAAM,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC;gBACjC,eAAe,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;aAC/C;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACtB;YAED,MAAM,MAAM,GAAG,IAAI,OAAO,EAAK,CAAC;YAChC,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;YACxC,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;YACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5B,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,EAAE,eAAe,EAAE,OAAc,CAAC,CAAC;YAEnF,IAAI,iBAAiB,CAAC,MAAM,EAAE;gBAC5B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aAC5C;iBAAM;gBACC,iBAAkB,CAAC,OAAO,GAAG,OAAO,CAAC;gBAC3C,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;aACrC;YAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC/B;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;SACrD;IACH,CAAC;IAED,WAAW,CAAC,GAAQ;QAClB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAClB,CAAC;IAED,cAAc,CAAC,KAAmB;QAChC,IAAI,KAAK,KAAK,IAAI,CAAC,gBAAgB,EAAE;YACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAQ,KAAM,CAAC,OAAO,CAAC,CAAC,CAAC;SAChE;IACH,CAAC;IAEO,WAAW,CAAC,KAAa;QAC/B,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,OAAO;SACR;QAED,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAC1B,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;QACzC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1B,MAAM,CAAC,QAAQ,EAAE,CAAC;QAClB,YAAY,CAAC,WAAW,EAAE,CAAC;IAC7B,CAAC;CACF"}
{"version": 3, "file": "bufferTime.js", "sources": ["../../../src/internal/operators/bufferTime.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAE3C,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAkElD,MAAM,UAAU,UAAU,CAAI,cAAsB;IAClD,IAAI,MAAM,GAAW,SAAS,CAAC,MAAM,CAAC;IAEtC,IAAI,SAAS,GAAkB,KAAK,CAAC;IACrC,IAAI,WAAW,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;QAChD,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5C,MAAM,EAAE,CAAC;KACV;IAED,IAAI,sBAAsB,GAAW,IAAI,CAAC;IAC1C,IAAI,MAAM,IAAI,CAAC,EAAE;QACf,sBAAsB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KACvC;IAED,IAAI,aAAa,GAAW,MAAM,CAAC,iBAAiB,CAAC;IACrD,IAAI,MAAM,IAAI,CAAC,EAAE;QACf,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9B;IAED,OAAO,SAAS,0BAA0B,CAAC,MAAqB;QAC9D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAI,cAAc,EAAE,sBAAsB,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;IAClH,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,kBAAkB;IACtB,YAAoB,cAAsB,EACtB,sBAA8B,EAC9B,aAAqB,EACrB,SAAwB;QAHxB,mBAAc,GAAd,cAAc,CAAQ;QACtB,2BAAsB,GAAtB,sBAAsB,CAAQ;QAC9B,kBAAa,GAAb,aAAa,CAAQ;QACrB,cAAS,GAAT,SAAS,CAAe;IAC5C,CAAC;IAED,IAAI,CAAC,UAA2B,EAAE,MAAW;QAC3C,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,oBAAoB,CAC9C,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CACjG,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,OAAO;IAAb;QACE,WAAM,GAAQ,EAAE,CAAC;IAEnB,CAAC;CAAA;AAmBD,MAAM,oBAAwB,SAAQ,UAAa;IAIjD,YAAY,WAA4B,EACpB,cAAsB,EACtB,sBAA8B,EAC9B,aAAqB,EACrB,SAAwB;QAC1C,KAAK,CAAC,WAAW,CAAC,CAAC;QAJD,mBAAc,GAAd,cAAc,CAAQ;QACtB,2BAAsB,GAAtB,sBAAsB,CAAQ;QAC9B,kBAAa,GAAb,aAAa,CAAQ;QACrB,cAAS,GAAT,SAAS,CAAe;QAPpC,aAAQ,GAAsB,EAAE,CAAC;QASvC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,sBAAsB,IAAI,IAAI,IAAI,sBAAsB,GAAG,CAAC,CAAC;QACjF,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,MAAM,iBAAiB,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;YACxE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC,0BAA0B,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC;SACnH;aAAM;YACL,MAAM,UAAU,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;YACjD,MAAM,aAAa,GAAyB,EAAE,cAAc,EAAE,sBAAsB,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;YACpH,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAsB,mBAAmB,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC;YACzH,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAuB,sBAAsB,EAAE,sBAAsB,EAAE,aAAa,CAAC,CAAC,CAAC;SACnH;IACH,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC5B,IAAI,mBAA+B,CAAC;QACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnB,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE;gBACvC,mBAAmB,GAAG,OAAO,CAAC;aAC/B;SACF;QAED,IAAI,mBAAmB,EAAE;YACvB,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;SACxC;IACH,CAAC;IAES,MAAM,CAAC,GAAQ;QACvB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAES,SAAS;QACjB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QACvC,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;YACjC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAClC;QACD,KAAK,CAAC,SAAS,EAAE,CAAC;IACpB,CAAC;IAGD,YAAY;QACV,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAES,YAAY,CAAC,OAAmB;QACxC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC3B,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACxC,WAAW,CAAC,WAAW,EAAE,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;YACrC,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;YAC3C,MAAM,iBAAiB,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;YACxE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,0BAA0B,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC;SACxH;IACH,CAAC;IAED,WAAW;QACT,MAAM,OAAO,GAAe,IAAI,OAAO,EAAK,CAAC;QAC7C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,YAAY,CAAC,OAAmB;QAC9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE/B,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,IAAI,WAAW,IAAI,CAAC,EAAE;YACpB,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;SAC/C;IACH,CAAC;CACF;AAED,SAAS,0BAA0B,CAA6B,KAAU;IACxE,MAAM,UAAU,GAA8B,KAAK,CAAC,UAAU,CAAC;IAE/D,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC;IAClC,IAAI,WAAW,EAAE;QACf,UAAU,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;KACtC;IAED,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;QACtB,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QACzC,KAAK,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC;KACxE;AACH,CAAC;AAED,SAAS,sBAAsB,CAAiD,KAA2B;IACzG,MAAM,EAAE,sBAAsB,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;IAChF,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;IACzC,MAAM,MAAM,GAA0C,IAAI,CAAC;IAC3D,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;QACtB,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAsB,mBAAmB,EAAE,cAAc,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QAC5I,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,sBAAsB,CAAC,CAAC;KAChD;AACH,CAAC;AAED,SAAS,mBAAmB,CAAI,GAAwB;IACtD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC;IACpC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AACnC,CAAC"}
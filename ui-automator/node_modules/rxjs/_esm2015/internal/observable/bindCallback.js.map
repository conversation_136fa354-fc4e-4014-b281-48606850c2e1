{"version": 3, "file": "bindCallback.js", "sources": ["../../../src/internal/observable/bindCallback.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAE/C,OAAO,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAC;AACvC,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAC1C,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AA4KlD,MAAM,UAAU,YAAY,CAC1B,YAAsB,EACtB,cAAuC,EACvC,SAAyB;IAEzB,IAAI,cAAc,EAAE;QAClB,IAAI,WAAW,CAAC,cAAc,CAAC,EAAE;YAC/B,SAAS,GAAG,cAAc,CAAC;SAC5B;aAAM;YAEL,OAAO,CAAC,GAAG,IAAW,EAAE,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAC5E,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAC9E,CAAC;SACH;KACF;IAED,OAAO,UAAqB,GAAG,IAAW;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC;QACrB,IAAI,OAAwB,CAAC;QAC7B,MAAM,MAAM,GAAG;YACb,OAAO;YACP,OAAO;YACP,YAAY;YACZ,SAAS;SACV,CAAC;QACF,OAAO,IAAI,UAAU,CAAI,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,SAAS,EAAE;gBACd,IAAI,CAAC,OAAO,EAAE;oBACZ,OAAO,GAAG,IAAI,YAAY,EAAK,CAAC;oBAChC,MAAM,OAAO,GAAG,CAAC,GAAG,SAAgB,EAAE,EAAE;wBACtC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;wBAC/D,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACrB,CAAC,CAAC;oBAEF,IAAI;wBACF,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;qBACjD;oBAAC,OAAO,GAAG,EAAE;wBACZ,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE;4BAC3B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;yBACpB;6BAAM;4BACL,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yBACnB;qBACF;iBACF;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;aACtC;iBAAM;gBACL,MAAM,KAAK,GAAqB;oBAC9B,IAAI,EAAE,UAAU,EAAE,MAAM;iBACzB,CAAC;gBACF,OAAO,SAAS,CAAC,QAAQ,CAAmB,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;aACjE;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAeD,SAAS,QAAQ,CAA6C,KAAuB;IACnF,MAAM,IAAI,GAAG,IAAI,CAAC;IAClB,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;IAC3C,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC;IACpD,IAAI,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;IACzB,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,YAAY,EAAK,CAAC;QAEjD,MAAM,OAAO,GAAG,CAAC,GAAG,SAAgB,EAAE,EAAE;YACtC,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAC/D,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAe,YAAY,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QAClF,CAAC,CAAC;QAEF,IAAI;YACF,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;SACjD;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACpB;KACF;IAED,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AAC1C,CAAC;AAOD,SAAS,YAAY,CAAyC,KAAmB;IAC/E,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;IACjC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpB,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrB,CAAC;AAOD,SAAS,aAAa,CAA0C,KAAoB;IAClF,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;IAC/B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACrB,CAAC"}
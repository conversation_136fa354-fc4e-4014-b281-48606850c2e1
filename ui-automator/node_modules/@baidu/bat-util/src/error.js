/**
 * @create 邱立楷@2022.08.08
 */
class JsonrpcError extends Error {
    constructor({ code, message, data }) {
        super(message);
        this.type = 'JsonrpcError';
        this.code = code;
        this.message = message;
        this.data = data;
    }
}

class DeviceError extends Error {
    constructor(message, detail = '') {
        super(message);
        this.type = 'DeviceError';
        this.detail = detail;
    }
}

class ParamsError extends Error {
    constructor(message, detail = '') {
        super(message);
        this.type = 'ParamsError';
        this.detail = detail;
    }
}

class ConfigError extends Error {
    constructor(message, detail = '') {
        super(message);
        this.type = 'ConfigError';
        this.detail = detail;
    }
}

module.exports = {
    JsonrpcError,
    DeviceError,
    ParamsError,
    ConfigError
};
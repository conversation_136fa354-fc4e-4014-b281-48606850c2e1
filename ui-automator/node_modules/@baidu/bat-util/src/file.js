/**
 * @create 邱立楷@2022.05.13
 */
const fs = require('fs');
const childProcess = require('child_process');
const unzipper = require('unzipper');
const path = require('path');
const axios = require('axios');

/**
 * 目录文件不存在则创建目录文件
 *
 * @param {string} dir 目录路径
 */
const mkdir = dir => {
    if (fs.existsSync(dir)) {
        return;
    }
    fs.mkdirSync(dir);
};

/**
 * 删除目录
 *
 * @param {string} dir 目录路径
 */
const rmdir = dir => {
    dir = path.resolve(dir);
    if (!fs.existsSync(dir)) {
        return;
    }

    // 去除尾部 /
    if (dir && '/' === dir.slice(-1)) {
        dir = dir.slice(0, dir.length - 1);
    }

    // 文件或软链接
    if (!fs.statSync(dir).isDirectory() || fs.lstatSync(dir).isSymbolicLink()) {
        fs.unlinkSync(dir);
        return;
    }

    // 目录
    for (let file of fs.readdirSync(dir)) {
        rmdir(`${dir}/${file}`);
    }

    // 删除目录
    fs.rmdirSync(dir);
};

/**
 * 清空目录
 *
 * @param {string} dir 目录路径
 */
const clearDir = dir => {
    rmdir(dir);
    mkdir(dir);
};

/**
 * 下载文件到指定路径
 *
 * @param {string} url 文件下载地址
 * @param {string} path 文件下载到的路径
 * @return {Promise}
 */
const wget = async (url, path) => axios.get(url, { responseType: 'arraybuffer' })
    .then(res => fs.writeFileSync(path, res.data))
    .catch(err => {
        throw new Error(`wget fail: ${url} ${err.message}`);
    });

const unzip = async (zipPath, outputPath) =>
    await new Promise(async (resolve, reject) => {
        fs.createReadStream(zipPath)
            .pipe(unzipper.Extract({ path: outputPath }))
            .on('close', () => resolve())
            .on('error', err => reject(err));
    });

const findFile = async (dirPath, fileName) => {
    return await new Promise(async (resolve, reject) => {
        try {
            let handler = childProcess.spawn(
                `find ${dirPath} -name ${fileName}`, { shell: true }
            );
            let resultBuf = Buffer.from('');
            handler.stdout.on('data', res => resultBuf += res);
            handler.stderr.on('data', () => { });
            handler.on('close', () => resolve(resultBuf.toString().split('\n')[0]));
        }
        catch {
            resolve('');
        }
    });
}

module.exports = { mkdir, rmdir, clearDir, wget, unzip, findFile };

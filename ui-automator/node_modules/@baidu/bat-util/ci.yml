Global:
    version: "2.0"
    group_email: <EMAIL>
Default:
    profile:
        - build
Profiles:
    - profile:
      name: build
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - nodejs: 20.0.0
            - yarn: 1.22.4
      build:
        command: sh scripts/pipe.sh
      artifacts:
        release: true
      cache:
        enable: true
        paths:
            - ~/.yarn
            - .yarn/install-state.gz
      check:
        - enable: true
          reuse: TASK
